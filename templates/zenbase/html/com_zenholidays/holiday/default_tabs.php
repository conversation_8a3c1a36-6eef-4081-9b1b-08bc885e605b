<?php
// No direct access.
defined('_JEXEC') or die;

// Import required classes
jimport('joomla.plugin.helper');
jimport('joomla.application.component.model');
jimport('joomla.application.component.helper');
use <PERSON><PERSON><PERSON>\Registry\Registry as JRegistry;
use <PERSON><PERSON><PERSON>\CMS\Factory as JFactory;
use Jo<PERSON><PERSON>\CMS\Language\Text as JText;
use <PERSON><PERSON><PERSON>\CMS\Plugin\PluginHelper as JPluginHelper;

$holidayItinerary = isset($this->item->default_version->itinerary) ? $this->item->default_version->itinerary : array();

// Check for extensions
$hasExtensions = isset($this->item->activities) && !empty($this->item->activities);

// Get copy items
$foodAccommodation = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'food-accommodation');
$holFAQ = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'faq');

// Copy items are loaded using getCopyItemsByAlias method
// Note: The overview template now uses a more direct approach to avoid category hierarchy issues

// Create helper instance
$helper = new ZenbaseCustomHelpers();

// Debug logging
$debug = JDEBUG;
if ($debug) {
    echo "<!-- Tab Debug Start -->\n";
    echo "<!-- Holiday Itinerary: " . (!empty($holidayItinerary) ? 'Exists' : 'Empty') . " -->\n";
    echo "<!-- Has Extensions: " . ($hasExtensions ? 'Yes' : 'No') . " -->\n";
    echo "<!-- Food Accommodation: " . ($foodAccommodation ? 'Exists' : 'Empty') . " -->\n";
    echo "<!-- FAQ: " . ($holFAQ ? 'Exists' : 'Empty') . " -->\n";
}
?>

<div class="zen-holiday__tabs d-none d-lg-block" id="desktop-tabs">
	<div class="container">
		<div class="row">
			<div class="col-12">
				<div class="zen-tab">
					<ul class="zen-tab__container nav" role="tablist" id="holTab">
						<li class="nav-item zen-tab__heading-item">
							<a class="nav-link active js-default-tab" href="#<?= $helper->toggleData($tab01); ?>-tab-content" id="<?= $helper->toggleData($tab01); ?>-tab" data-bs-toggle="tab" role="tab" aria-controls="<?= $helper->toggleData($tab01); ?>-tab-content" aria-selected="true" data-scroll_target="holidayContent">
								<span><?php echo $tab01_display; ?></span>
							</a>
						</li>

						<?php if (!empty($holidayItinerary)): ?>
						<li class="nav-item zen-tab__heading-item">
							<a class="nav-link" href="#<?= $helper->toggleData($tab02); ?>-tab-content" id="<?= $helper->toggleData($tab02); ?>-tab" data-bs-toggle="tab" role="tab" aria-controls="<?= $helper->toggleData($tab02); ?>-tab-content" aria-selected="false" data-scroll_target="holidayContent">
								<span><?php echo $tab02_display; ?></span>
							</a>
						</li>
						<?php endif; ?>

						<li class="nav-item zen-tab__heading-item">
							<a class="nav-link" href="#<?= $helper->toggleData($tab03); ?>-tab-content" id="<?= $helper->toggleData($tab03); ?>-tab" data-bs-toggle="tab" role="tab" aria-controls="<?= $helper->toggleData($tab03); ?>-tab-content" aria-selected="false" data-scroll_target="holidayContent">
								<span><?php echo $tab03_display; ?></span>
							</a>
						</li>

						<?php if ($foodAccommodation && isset($foodAccommodation['food-accommodation']) && isset($foodAccommodation['food-accommodation']->items)): ?>
						<li class="nav-item zen-tab__heading-item">
							<a class="nav-link" href="#<?= $helper->toggleData($tab08); ?>-tab-content" id="<?= $helper->toggleData($tab08); ?>-tab" data-bs-toggle="tab" role="tab" aria-controls="<?= $helper->toggleData($tab08); ?>-tab-content" aria-selected="false" data-scroll_target="holidayContent">
								<span><?php echo $tab08_display; ?></span>
							</a>
						</li>
						<?php endif; ?>

						<?php if ($hasExtensions): ?>
						<li class="nav-item zen-tab__heading-item">
							<a class="nav-link" href="#<?= $helper->toggleData($tab04); ?>-tab-content" id="<?= $helper->toggleData($tab04); ?>-tab" data-bs-toggle="tab" role="tab" aria-controls="<?= $helper->toggleData($tab04); ?>-tab-content" aria-selected="false" data-scroll_target="holidayContent">
								<span><?php echo $tab04_display; ?></span>
							</a>
						</li>
						<?php endif; ?>

						<?php if ($holFAQ && isset($holFAQ['faq']) && isset($holFAQ['faq']->items)): ?>
						<li class="nav-item zen-tab__heading-item">
							<a class="nav-link me-0" href="#<?= $helper->toggleData($tab06); ?>-tab-content" id="<?= $helper->toggleData($tab06); ?>-tab" data-bs-toggle="tab" role="tab" aria-controls="<?= $helper->toggleData($tab06); ?>-tab-content" aria-selected="false" data-scroll_target="holidayContent">
								<span><?php echo $tab06_display; ?></span>
							</a>
						</li>
						<?php endif; ?>
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="zen-holiday__tabs d-lg-none" id="mobile-tabs">
	<div class="container">
		<div class="row">
			<div class="col-12">
				<div class="zen-tab">
					<div class="zen-tab__container accordion" id="holTabAccordion">
						<div class="zen-tab__heading">
							<button class="zen-tab__button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#<?= $helper->toggleData($tab01); ?>-tab-content" aria-expanded="false" aria-controls="<?= $helper->toggleData($tab01); ?>-tab-content">
								<?php echo $tab01_display; ?>
							</button>
						</div>

						<?php if (!empty($holidayItinerary)): ?>
						<div class="zen-tab__heading">
							<button class="zen-tab__button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#<?= $helper->toggleData($tab02); ?>-tab-content" aria-expanded="false" aria-controls="<?= $helper->toggleData($tab02); ?>-tab-content">
								<?php echo $tab02_display; ?>
							</button>
						</div>
						<?php endif; ?>

						<div class="zen-tab__heading">
							<button class="zen-tab__button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#<?= $helper->toggleData($tab03); ?>-tab-content" aria-expanded="false" aria-controls="<?= $helper->toggleData($tab03); ?>-tab-content">
								<?php echo $tab03_display; ?>
							</button>
						</div>

						<?php if ($foodAccommodation && isset($foodAccommodation['food-accommodation']) && isset($foodAccommodation['food-accommodation']->items)): ?>
						<div class="zen-tab__heading">
							<button class="zen-tab__button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#<?= $helper->toggleData($tab08); ?>-tab-content" aria-expanded="false" aria-controls="<?= $helper->toggleData($tab08); ?>-tab-content">
								<?php echo $tab08_display; ?>
							</button>
						</div>
						<?php endif; ?>

						<?php if ($hasExtensions): ?>
						<div class="zen-tab__heading">
							<button class="zen-tab__button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#<?= $helper->toggleData($tab04); ?>-tab-content" aria-expanded="false" aria-controls="<?= $helper->toggleData($tab04); ?>-tab-content">
								<?php echo $tab04_display; ?>
							</button>
						</div>
						<?php endif; ?>

						<?php if ($holFAQ && isset($holFAQ['faq']) && isset($holFAQ['faq']->items)): ?>
						<div class="zen-tab__heading">
							<button class="zen-tab__button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#<?= $helper->toggleData($tab06); ?>-tab-content" aria-expanded="false" aria-controls="<?= $helper->toggleData($tab06); ?>-tab-content">
								<?php echo $tab06_display; ?>
							</button>
						</div>
						<?php endif; ?>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<?php if ($debug): ?>
    <!-- Tab Debug End -->
<?php endif; ?>